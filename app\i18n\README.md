# 国际化配置文件 (Internationalization Configuration)

这个文件夹包含了 Visual Define-XML Editor 的国际化配置文件，用于管理多语言支持。

## 文件结构

```
app/i18n/
├── README.md          # 说明文档
├── index.js           # 主配置文件，管理所有语言
├── en.js              # 英语翻译配置
└── zh-CN.js           # 简体中文翻译配置
```

## 如何添加新语言

1. **创建新的语言文件**
   在 `app/i18n/` 文件夹中创建新的语言文件，例如 `fr.js`（法语）：

   ```javascript
   // French translations
   export default {
       'Studies': 'Études',
       'Editor': 'Éditeur',
       // ... 其他翻译
   };
   ```

2. **在主配置文件中注册新语言**
   编辑 `app/i18n/index.js`，添加新语言的导入和配置：

   ```javascript
   import fr from './fr.js';

   export const languages = {
       en: { /* ... */ },
       'zh-CN': { /* ... */ },
       fr: {
           name: 'French',
           nativeName: 'Français',
           code: 'fr',
           translations: fr
       }
   };
   ```

## 如何添加新的翻译键

1. **在所有语言文件中添加相同的键**
   确保在 `en.js`、`zh-CN.js` 和其他语言文件中都添加相同的键：

   ```javascript
   // en.js
   'New Feature': 'New Feature',

   // zh-CN.js
   'New Feature': '新功能',
   ```

2. **在代码中使用翻译键**
   ```javascript
   import { t } from 'utils/i18n.js';
   
   const text = t('New Feature');
   ```

## 翻译键的组织结构

翻译键按功能模块组织：

- **Main Menu** - 主菜单项
- **Settings** - 设置相关
- **Common** - 通用按钮和操作
- **File Menu** - 文件菜单
- **Editor** - 编辑器相关
- **Editor Tabs** - 编辑器标签页
- **Table Headers and Labels** - 表格标题和标签
- **Buttons and Actions** - 按钮和操作
- **Search and Filter** - 搜索和筛选
- **Standards Tab** - 标准标签页
- **XML Generation Comments** - XML 生成注释
- **Standard Constants** - 标准常量
  - Code List Types - 代码列表类型
  - Origin Types - 来源类型
  - Document Types - 文档类型
  - Class Types - 类别类型
  - Variable Roles - 变量角色
  - ARM Analysis - ARM 分析

## 最佳实践

1. **保持键名一致**
   - 使用英语作为键名
   - 键名应该描述性强，易于理解
   - 避免使用缩写

2. **翻译质量**
   - 确保翻译准确、专业
   - 保持术语一致性
   - 考虑上下文和用户体验

3. **维护性**
   - 定期检查是否有遗漏的翻译
   - 保持所有语言文件的同步
   - 添加注释说明特殊的翻译需求

## API 参考

### 主要函数

- `t(key, defaultValue)` - 使用当前语言翻译
- `tWithLang(key, language, defaultValue)` - 使用指定语言翻译
- `setLanguage(language)` - 设置当前语言
- `getCurrentLanguage()` - 获取当前语言
- `initLanguage(language)` - 初始化语言设置

### 配置函数

- `getAvailableLanguages()` - 获取可用语言列表
- `getLanguageInfo(code)` - 获取语言信息
- `getTranslations(languageCode)` - 获取指定语言的所有翻译
- `isLanguageSupported(languageCode)` - 检查语言是否支持
- `getDefaultLanguage()` - 获取默认语言

## 向后兼容性

这个新的配置系统完全向后兼容，现有的代码无需修改即可继续工作。原有的 `utils/i18n.js` 文件现在从配置文件加载翻译内容。
