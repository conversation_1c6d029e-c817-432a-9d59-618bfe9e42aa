{"presets": [["env", {"targets": {"node": 10}, "useBuiltIns": true}], "stage-0", "react"], "plugins": ["add-module-exports", "syntax-async-generators", ["prismjs", {"languages": ["sas", "r", "python"], "plugins": ["line-numbers"], "theme": "default", "css": true}], "react-hot-loader/babel"], "env": {"production": {"presets": ["react-optimize"], "plugins": ["syntax-async-generators", "dev-expression"]}, "development": {"plugins": ["transform-class-properties", "transform-es2015-classes", ["flow-runtime", {"assert": true, "annotate": true}]]}}}