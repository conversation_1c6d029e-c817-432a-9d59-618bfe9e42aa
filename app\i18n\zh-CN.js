/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018 <PERSON>losov                                                *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

// Simplified Chinese translations
export default {
    // Main Menu
    'Studies': '研究',
    'Editor': '编辑器',
    'Controlled Terminology': '受控术语',
    'CDISC Library': 'CDISC 库',
    'Settings': '设置',
    'About': '关于',
    'Keyboard Shortcuts': '键盘快捷键',
    'Update': '更新',
    'Find in Page': '页面查找',
    'Search Studies': '搜索研究',
    'History': '历史记录',
    'Save': '保存',
    'Save As': '另存为',
    'Print': '打印',
    'Comments/Methods': '注释/方法',
    'Tools': '工具',
    'Review Mode': '审阅模式',
    'Quit': '退出',

    // Settings
    'General Settings': '常规设置',
    'System': '系统',
    'User Name': '用户名',
    'Controlled Terminology Folder': '受控术语文件夹',
    'Language': '语言',
    'English': '英语',
    'Simplified Chinese': '简体中文',

    // Common
    'Search': '搜索',
    'Cancel': '取消',
    'OK': '确定',
    'Yes': '是',
    'No': '否',
    'Close': '关闭',
    'Open': '打开',
    'Delete': '删除',
    'Edit': '编辑',
    'Add': '添加',
    'Remove': '移除',
    'Copy': '复制',
    'Paste': '粘贴',
    'Cut': '剪切',
    'Undo': '撤销',
    'Redo': '重做',
    'Confirm Close': '确认关闭',
    'You have not saved changes to a comment. By continuing all of the unsaved changes will be lost.': '您尚未保存对注释所做的更改。继续操作将丢失所有未保存的更改。',
    'Continue': '继续',
    'Reply': '回复',
    'Hide': '隐藏',
    'Show': '显示',
    'replies': '回复',
    'Unresolve': '未解决',
    'Resolve': '解决',
    
    // File Menu
    'File': '文件',
    'Save As Define-XML 2.0': '另存为 Define-XML 2.0',
    'View': '查看',
    'Help': '帮助',

    // Editor
    'No Define-XML documents are selected for editing. Select a Define-XML document to edit on the': '没有选择要编辑的 Define-XML 文档。请在',
    'page.': '页面选择要编辑的 Define-XML 文档。',
    'Loading Define-XML.': '正在加载 Define-XML。',
    'Taking too long? Use Ctrl+M to open the menu.': '加载时间过长？使用 Ctrl+M 打开菜单。',

    // Editor Tabs
    'Standards': '标准',
    'Datasets': '数据集',
    'Variables': '变量',
    'Codelists': '代码列表',
    'Coded Values': '编码值',
    'Documents': '文档',
    'Result Displays': '结果显示',
    'Analysis Results': '分析结果',
    'Review Comments': '审阅注释',

    // Table Headers and Labels
    'Name': '名称',
    'Label': '标签',
    'Type': '类型',
    'Length': '长度',
    'Order': '顺序',
    'Description': '描述',
    'Comment': '注释',
    'Method': '方法',
    'Value': '值',
    'Code': '代码',
    'Decode': '解码',
    'Status': '状态',
    'Version': '版本',
    'Date': '日期',
    'Author': '作者',
    'Title': '标题',
    'Location': '位置',
    'Format': '格式',
    'Class': '类别',
    'Structure': '结构',
    'Purpose': '用途',
    'Keys': '键',
    'Mandatory': '必填',
    'Repeating': '重复',
    'Reference': '引用',
    'Standard': '标准',
    'Domain': '域',
    'Variable': '变量',
    'Dataset': '数据集',
    'Codelist': '代码列表',

    // Buttons and Actions
    'Update': '更新',
    'Columns': '列',
    'Import Metadata': '导入元数据',
    'Add Variable': '添加变量',
    'Add Dataset': '添加数据集',
    'New Variable': '新变量',
    'New Dataset': '新数据集',
    'This Define': '当前Define',
    'Another Define': '其他Define',
    'Options': '选项',
    'Global Variables & Study OID': '全局变量和研究OID',
    'Study OID': '研究OID',
    'Study Name': '研究名称',
    'Protocol Name': '协议名称',
    'Analysis Variables': '分析变量',
    'Add Reference Dataset': '添加参考数据集',
    'Remove Variable': '移除变量',

    // Search and Filter
    'Ctrl+F': 'Ctrl+F',
    'Expand VLM': '展开VLM',
    'Collapse VLM': '折叠VLM',
    'Filter': '筛选',

    // Standards Tab
    'Global Variables & Study OID': '全局变量和研究OID',
    'Study Description': '研究描述',
    'MetaDataVersion': '元数据版本',
    'Standard': '标准',
    'Controlled Terminology': '受控术语',
    'ODM Attributes': 'ODM属性',
    'Other Attributes': '其他属性',
    'Remove Standard': '移除标准',
    'Analysis Results Metadata': '分析结果元数据',
    'File OID': '文件OID',
    'As Of Date Time': '截止日期时间',
    'Originator': '创建者',
    'Stylesheet Location': '样式表位置',
    'Path to File': '文件路径',
    'Last Modified': '最后修改',
    'ODM Attributes & Stylesheet location': 'ODM属性和样式表位置',
    'Sponsor Name': '申办方名称',
    'Database Query Datetime': '数据库查询日期时间',
    'Visual Define-XML Editor Attributes': 'Visual Define-XML编辑器属性',
    'Define-XML Name': 'Define-XML名称',
    'Define-XML Location': 'Define-XML位置',
    'Model': '模型',
    'Version': '版本',
    '# Codelists': '# 代码列表',
    'Remove Controlled Terminology': '移除受控术语',
    'Add Controlled Terminology': '添加受控术语',

    // XML Generation Comments
    'ItemGroup Definitions': '数据集定义',
    'ItemDef Definitions': '数据项定义',
    'Codelist Definitions': '代码列表定义',
    'Method Definitions': '方法定义',
    'Comment Definitions': '注释定义',
    'Leaf Definitions': '叶子定义',
    'Analysis Result Display Definitions': '分析结果显示定义',

    // Standard Constants - Code List Types
    'Enumeration': '枚举',
    'Decoded': '解码',
    'External': '外部',

    // Standard Constants - Origin Types
    'Derived': '派生',
    'Assigned': '分配',
    'Predecessor': '前置',
    'CRF': 'CRF',
    'Protocol': '协议',
    'eDT': 'eDT',
    'COLLECTED': '收集',
    'DERIVED': '派生',
    'OTHER': '其他',
    'NOT AVAILABLE': '不可用',

    // Standard Constants - Document Types
    'Annotated CRF': '注释CRF',
    'Supplemental Document': '补充文档',
    'Other': '其他',

    // Standard Constants - Class Types
    'SUBJECT LEVEL ANALYSIS DATASET': '受试者级别分析数据集',
    'OCCURRENCE DATA STRUCTURE': '事件数据结构',
    'BASIC DATA STRUCTURE': '基础数据结构',
    'INTEGRATED SUBJECT LEVEL': '集成受试者级别',
    'INTEGRATED OCCURRENCE DATA STRUCTURE': '集成事件数据结构',
    'INTEGRATED BASIC DATA STRUCTURE': '集成基础数据结构',
    'ADAM OTHER': 'ADaM其他',
    'TRIAL DESIGN': '试验设计',
    'SPECIAL PURPOSE': '特殊用途',
    'INTERVENTIONS': '干预',
    'EVENTS': '事件',
    'FINDINGS': '发现',
    'FINDINGS ABOUT': '关于发现',
    'RELATIONSHIP': '关系',
    'STUDY REFERENCE': '研究参考',

    // Standard Constants - Variable Roles
    'Identifier': '标识符',
    'Topic': '主题',
    'Timing': '时间',
    'Grouping Qualifier': '分组限定符',
    'Result Qualifier': '结果限定符',
    'Synonym Qualifier': '同义词限定符',
    'Record Qualifier': '记录限定符',
    'Variable Qualifier': '变量限定符',
    'Rule': '规则',

    // Standard Constants - ARM Analysis
    'Data Driven': '数据驱动',
    'Requested by Regulatory Agency': '监管机构要求',
    'Specified in Protocol': '协议中规定',
    'Specified in SAP': 'SAP中规定',
    'Exploratory Outcome Measure': '探索性结局指标',
    'Primary Outcome Measure': '主要结局指标',
    'Secondary Outcome Measure': '次要结局指标',
};
