/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018 <PERSON>losov                                                *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

// Internationalization utilities for development and maintenance
import { languages, getAvailableLanguages, getTranslations } from './index.js';

/**
 * Find missing translation keys across languages
 * @returns {Object} Object with language codes as keys and arrays of missing keys as values
 */
export const findMissingTranslations = () => {
    const availableLanguages = getAvailableLanguages();
    const allKeys = new Set();
    const missingKeys = {};

    // Collect all unique keys from all languages
    availableLanguages.forEach(lang => {
        const translations = getTranslations(lang);
        if (translations) {
            Object.keys(translations).forEach(key => allKeys.add(key));
        }
    });

    // Find missing keys for each language
    availableLanguages.forEach(lang => {
        const translations = getTranslations(lang);
        missingKeys[lang] = [];
        
        if (translations) {
            allKeys.forEach(key => {
                if (!(key in translations)) {
                    missingKeys[lang].push(key);
                }
            });
        } else {
            missingKeys[lang] = Array.from(allKeys);
        }
    });

    return missingKeys;
};

/**
 * Find unused translation keys (keys that exist but are not used in code)
 * Note: This is a basic implementation that would need to be enhanced with actual code scanning
 * @returns {Array} Array of potentially unused keys
 */
export const findUnusedTranslations = () => {
    // This would require scanning the codebase for t() and tWithLang() calls
    // For now, return empty array as placeholder
    console.warn('findUnusedTranslations: This function requires code scanning implementation');
    return [];
};

/**
 * Validate translation completeness
 * @returns {Object} Validation report
 */
export const validateTranslations = () => {
    const missingKeys = findMissingTranslations();
    const availableLanguages = getAvailableLanguages();
    const report = {
        isComplete: true,
        totalKeys: 0,
        languages: {}
    };

    // Calculate total unique keys
    const allKeys = new Set();
    availableLanguages.forEach(lang => {
        const translations = getTranslations(lang);
        if (translations) {
            Object.keys(translations).forEach(key => allKeys.add(key));
        }
    });
    report.totalKeys = allKeys.size;

    // Generate report for each language
    availableLanguages.forEach(lang => {
        const translations = getTranslations(lang);
        const missing = missingKeys[lang] || [];
        const completeness = translations ? 
            ((Object.keys(translations).length / report.totalKeys) * 100).toFixed(2) : 0;

        report.languages[lang] = {
            name: languages[lang].name,
            nativeName: languages[lang].nativeName,
            totalTranslations: translations ? Object.keys(translations).length : 0,
            missingKeys: missing,
            missingCount: missing.length,
            completeness: `${completeness}%`,
            isComplete: missing.length === 0
        };

        if (missing.length > 0) {
            report.isComplete = false;
        }
    });

    return report;
};

/**
 * Get translation statistics
 * @returns {Object} Statistics about translations
 */
export const getTranslationStats = () => {
    const availableLanguages = getAvailableLanguages();
    const stats = {
        totalLanguages: availableLanguages.length,
        languages: [],
        totalUniqueKeys: 0
    };

    const allKeys = new Set();
    
    availableLanguages.forEach(lang => {
        const translations = getTranslations(lang);
        const langInfo = languages[lang];
        
        if (translations) {
            Object.keys(translations).forEach(key => allKeys.add(key));
            
            stats.languages.push({
                code: lang,
                name: langInfo.name,
                nativeName: langInfo.nativeName,
                keyCount: Object.keys(translations).length
            });
        }
    });

    stats.totalUniqueKeys = allKeys.size;
    return stats;
};

/**
 * Export translations to JSON format (useful for external translation tools)
 * @param {string} languageCode - Language code to export
 * @returns {string} JSON string of translations
 */
export const exportTranslationsToJson = (languageCode) => {
    const translations = getTranslations(languageCode);
    if (!translations) {
        throw new Error(`Language ${languageCode} not found`);
    }
    return JSON.stringify(translations, null, 2);
};

/**
 * Compare two languages and find differences
 * @param {string} lang1 - First language code
 * @param {string} lang2 - Second language code
 * @returns {Object} Comparison result
 */
export const compareLanguages = (lang1, lang2) => {
    const translations1 = getTranslations(lang1);
    const translations2 = getTranslations(lang2);
    
    if (!translations1 || !translations2) {
        throw new Error('One or both languages not found');
    }

    const keys1 = new Set(Object.keys(translations1));
    const keys2 = new Set(Object.keys(translations2));
    
    const onlyInLang1 = Array.from(keys1).filter(key => !keys2.has(key));
    const onlyInLang2 = Array.from(keys2).filter(key => !keys1.has(key));
    const common = Array.from(keys1).filter(key => keys2.has(key));

    return {
        lang1: {
            code: lang1,
            name: languages[lang1].name,
            totalKeys: keys1.size,
            uniqueKeys: onlyInLang1
        },
        lang2: {
            code: lang2,
            name: languages[lang2].name,
            totalKeys: keys2.size,
            uniqueKeys: onlyInLang2
        },
        commonKeys: common,
        commonCount: common.length
    };
};

export default {
    findMissingTranslations,
    findUnusedTranslations,
    validateTranslations,
    getTranslationStats,
    exportTranslationsToJson,
    compareLanguages
};
