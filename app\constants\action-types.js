/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018 Dmitry Kolosov                                                *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of ME<PERSON><PERSON><PERSON>ABILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

export const ADD_ODM = 'ADD_ODM';
export const ADD_STDCONST = 'ADD_STDCONST';
export const DUMMY_ACTION = 'DUMMY_ACTION';
export const STDCDL_LOAD = 'STDCDL_LOAD';
export const STDCDL_DEL = 'STDCDL_DEL';
export const UPD_STDCT = 'UPD_STDCT';
export const UPD_STD = 'UPD_STD';
export const UPD_MODEL = 'UPD_MODEL';
export const UPD_ARMSTATUS = 'UPD_ARMSTATUS';
export const UPD_GLOBALVARSSTOID = 'UPD_GLOBALVARSSTOID';
export const UPD_ODMATTRS = 'UPD_ODMATTRS';
export const UPD_MDV = 'UPD_MDV';
export const UPD_ITEMGROUP = 'UPD_ITEMGROUP';
export const UPD_ITEMGROUPORDER = 'UPD_ITEMGROUPORDER';
export const ADD_ITEMGROUP = 'ADD_ITEMGROUP';
export const ADD_ITEMGROUPS = 'ADD_ITEMGROUPS';
export const DEL_ITEMGROUPS = 'DEL_ITEMGROUPS';
export const ADD_ITEMGROUPCOMMENT = 'ADD_ITEMGROUPCOMMENT';
export const UPD_ITEMGROUPCOMMENT = 'UPD_ITEMGROUPCOMMENT';
export const DEL_ITEMGROUPCOMMENT = 'DEL_ITEMGROUPCOMMENT';
export const REP_ITEMGROUPCOMMENT = 'REP_ITEMGROUPCOMMENT';
export const UPD_ITEMDEF = 'UPD_ITEMDEF';
export const UPD_ITEMREF = 'UPD_ITEMREF';
export const UPD_ITEMREFKEYORDER = 'UPD_ITEMREFKEYORDER';
export const UPD_ITEMREFORDER = 'UPD_ITEMREFORDER';
export const UPD_VLMITEMREFORDER = 'UPD_VLMITEMREFORDER';
export const UPD_ITEMCLDF = 'UPD_ITEMCLDF';
export const UPD_ITEMDESCRIPTION = 'UPD_ITEMDESCRIPTION';
export const UPD_ITEMSBULK = 'UPD_ITEMSBULK';
export const ADD_VAR = 'ADD_VAR';
export const ADD_VARS = 'ADD_VARS';
export const ADD_VALUELIST = 'ADD_VALUELIST';
export const ADD_VALUELISTFROMCODELIST = 'ADD_VALUELISTFROMCODELIST';
export const INSERT_VAR = 'INSERT_VAR';
export const INSERT_VALLVL = 'INSERT_VALLVL';
export const DEL_VARS = 'DEL_VARS';
export const ADD_CODELIST = 'ADD_CODELIST';
export const UPD_CODELIST = 'UPD_CODELIST';
export const UPD_CODELISTSTD = 'UPD_CODELISTSTD';
export const UPD_CODELISTSSTD = 'UPD_CODELISTSSTD';
export const UPD_CODELISTEXT = 'UPD_CODELISTEXT';
export const DEL_CODELISTS = 'DEL_CODELISTS';
export const UPD_CODELISTORDER = 'UPD_CODELISTORDER';
export const UPD_LINKCODELISTS = 'UPD_LINKCODELISTS';
export const UPD_CODEDVALUE = 'UPD_CODEDVALUE';
export const ADD_CODEDVALUE = 'ADD_CODEDVALUE';
export const ADD_CODEDVALUES = 'ADD_CODEDVALUES';
export const DEL_CODEDVALUES = 'DEL_CODEDVALUES';
export const UPD_CODEDVALUEORDER = 'UPD_CODEDVALUEORDER';
export const UPD_LEAFS = 'UPD_LEAFS';
export const UPD_LEAFORDER = 'UPD_LEAFORDER';
export const UPD_KEYORDER = 'UPD_KEYORDER';
export const UPD_NAMELABELWHERECLAUSE = 'UPD_NAMELABELWHERECLAUSE';
export const UPD_LOADACTUALDATA = 'UPD_LOADACTUALDATA';
export const DEL_RESULTDISPLAY = 'DEL_RESULTDISPLAY';
export const ADD_RESULTDISPLAY = 'ADD_RESULTDISPLAY';
export const ADD_RESULTDISPLAYS = 'ADD_RESULTDISPLAYS';
export const UPD_RESULTDISPLAY = 'UPD_RESULTDISPLAY';
export const UPD_RESULTDISPLAYORDER = 'UPD_RESULTDISPLAYORDER';
export const DEL_ANALYSISRESULT = 'DEL_ANALYSISRESULT';
export const ADD_ANALYSISRESULT = 'ADD_ANALYSISRESULT';
export const ADD_ANALYSISRESULTS = 'ADD_ANALYSISRESULTS';
export const UPD_ANALYSISRESULT = 'UPD_ANALYSISRESULT';
export const UPD_ANALYSISRESULTORDER = 'UPD_ANALYSISRESULTORDER';
export const ADD_REVIEWCOMMENT = 'ADD_REVIEWCOMMENT';
export const UPD_REVIEWCOMMENT = 'UPD_REVIEWCOMMENT';
export const DEL_REVIEWCOMMENT = 'DEL_REVIEWCOMMENT';
export const ADD_REPLYCOMMENT = 'ADD_REPLYCOMMENT';
export const UPD_RESOLVECOMMENT = 'UPD_RESOLVECOMMENT';
export const ADD_IMPORTMETADATA = 'ADD_IMPORTMETADATA';
export const DEL_DUPLICATECOMMENTS = 'DEL_DUPLICATECOMMENTS';
export const DEL_DUPLICATEMETHODS = 'DEL_DUPLICATEMETHODS';
export const UI_CHANGETAB = 'UI_CHANGETAB';
export const UI_CHANGEPAGE = 'UI_CHANGEPAGE';
export const UI_CHANGETABLEPAGEDETAILS = 'UI_CHANGETABLEPAGEDETAILS';
export const UI_TOGGLEROWSELECT = 'UI_TOGGLEROWSELECT';
export const UI_SETVLMSTATE = 'UI_SETVLMSTATE';
export const UI_SELECTGROUP = 'UI_SELECTGROUP';
export const UI_SELECTCOLUMNS = 'UI_SELECTCOLUMNS';
export const UI_TOGGLEMAINMENU = 'UI_TOGGLEMAINMENU';
export const UI_SETSTUDYORDERTYPE = 'UI_SETSTUDYORDERTYPE';
export const UI_TOGGLEADDDEFINEFORM = 'UI_TOGGLEADDDEFINEFORM';
export const UI_UPDATEFILTER = 'UI_UPDATEFILTER';
export const UI_LOADTABS = 'UI_LOADTABS';
export const UI_OPENMODAL = 'UI_OPENMODAL';
export const UI_CLOSEMODAL = 'UI_CLOSEMODAL';
export const UI_UPDMAIN = 'UI_UPDMAIN';
export const UI_TOGGLEREVIEWMODE = 'UI_TOGGLEREVIEWMODE';
export const UI_UPDCOPYBUFFER = 'UI_UPDCOPYBUFFER';
export const UI_TOGGLEREVIEWCOMMENTPANELS = 'UI_TOGGLEREVIEWCOMMENTPANELS';
export const UI_TOGGLEREVIEWCOMMENTSHOWRESOLVED = 'UI_TOGGLEREVIEWCOMMENTSHOWRESOLVED';
export const UI_OPENSNACKBAR = 'UI_OPENSNACKBAR';
export const UI_CLOSESNACKBAR = 'UI_CLOSESNACKBAR';
export const UI_CHANGECDISCLIBRARYVIEW = 'UI_CHANGECDISCLIBRARYVIEW';
export const UI_VARCHANGECDISCLIBRARYVIEW = 'UI_VARCHANGECDISCLIBRARYVIEW';
export const UI_ITEMGROUPCHANGECDISCLIBRARYVIEW = 'UI_ITEMGROUPCHANGECDISCLIBRARYVIEW';
export const UI_TOGGLECDISCLIBRARYITEMGROUPGRIDVIEW = 'UI_TOGGLECDISCLIBRARYITEMGROUPGRIDVIEW';
export const UI_VARTOGGLECDISCLIBRARYITEMGROUPGRIDVIEW = 'UI_VARTOGGLECDISCLIBRARYITEMGROUPGRIDVIEW';
export const UI_SAVECDISCLIBRARYINFO = 'UI_SAVECDISCLIBRARYINFO';
export const UI_CHANGECTVIEW = 'UI_CHANGECTVIEW';
export const UI_CHANGECTSETTINGS = 'UI_CHANGECTSETTINGS';
export const UI_TOGGLECTCDISCLIBRARY = 'UI_TOGGLECTCDISCLIBRARY';
export const UI_ADDITEMCHANGETAB = 'UI_ADDITEMCHANGETAB';
export const STG_UPDATESETTINGS = 'STG_UPDATESETTINGS';
export const STUDY_ADD = 'STUDY_ADD';
export const STUDY_DEL = 'STUDY_DEL';
export const STUDY_UPD = 'STUDY_UPD';
export const STUDY_IMPORT = 'STUDY_IMPORT';
export const STUDY_UPDORDER = 'STUDY_UPDORDER';
export const STUDY_UPDDEFINEORDER = 'STUDY_UPDDEFINEORDER';
export const DEFINE_ADD = 'DEFINE_ADD';
export const DEFINE_UPD = 'DEFINE_UPD';
export const DEFINE_DEL = 'DEFINE_DEL';
export const CT_ADD = 'CT_ADD';
export const CT_UPD = 'CT_UPD';
export const CT_DEL = 'CT_DEL';
export const CT_RELOAD = 'CT_RELOAD';
export const APP_SAVE = 'APP_SAVE';
export const APP_QUIT = 'APP_QUIT';
export const SD_UPDATESEACHINFO = 'SD_UPDATESEACHINFO';

// Labels for Undo/Action History
export const actionLabels = {
    'ADD_ODM': 'Load Define-XML',
    'ADD_STDCONST': 'Add standard constants',
    'STDCDL_LOAD': 'Load CT',
    'STDCDL_DEL': 'Remove CT',
    'UPD_STDCT': 'Update standard Controlled Terminology ',
    'UPD_STD': 'Update standard',
    'UPD_MODEL': 'Update model',
    'UPD_ARMSTATUS': 'Update ARM status',
    'UPD_GLOBALVARSSTOID': 'Update global variables',
    'UPD_ODMATTRS': 'Update ODM attributes',
    'UPD_MDV': 'Update MetadataVersion',
    'UPD_ITEMGROUP': 'Update dataset',
    'UPD_ITEMGROUPORDER': 'Update dataset order',
    'ADD_ITEMGROUP': 'Add dataset',
    'ADD_ITEMGROUPS': 'Add datasets',
    'DEL_ITEMGROUPS': 'Delete datasets',
    'ADD_ITEMGROUPCOMMENT': 'Add dataset comment',
    'UPD_ITEMGROUPCOMMENT': 'Update dataset comment',
    'DEL_ITEMGROUPCOMMENT': 'Delete dataset comment',
    'REP_ITEMGROUPCOMMENT': 'Replace dataset comment ',
    'UPD_ITEMDEF': 'Update variable',
    'UPD_ITEMREF': 'Update variable',
    'UPD_ITEMREFKEYORDER': 'Update variable key order',
    'UPD_ITEMREFORDER': 'Update variable order',
    'UPD_VLMITEMREFORDER': 'Update VLM order',
    'UPD_ITEMCLDF': 'Update codelist and display format',
    'UPD_ITEMDESCRIPTION': 'Update variable description',
    'UPD_ITEMSBULK': 'Bulk variable update',
    'ADD_VAR': 'Add variable',
    'ADD_VARS': 'Add variables',
    'ADD_VALUELIST': 'Add VLM',
    'ADD_VALUELISTFROMCODELIST': 'Create VLM from a Codelist',
    'INSERT_VAR': 'Insert variable ',
    'INSERT_VALLVL': 'Insert VLM ',
    'DEL_VARS': 'Delete variables',
    'ADD_CODELIST': 'Add codelist',
    'UPD_CODELIST': 'Update codelist',
    'UPD_CODELISTSTD': 'Update codelist standard',
    'UPD_CODELISTSSTD': 'Update codelists standard',
    'UPD_CODELISTEXT': 'Update external codelist',
    'DEL_CODELISTS': 'Delete codelist',
    'UPD_CODELISTORDER': 'Update codelist order',
    'UPD_LINKCODELISTS': 'Update linked codelist',
    'UPD_CODEDVALUE': 'Update coded value',
    'ADD_CODEDVALUE': 'Add coded value',
    'ADD_CODEDVALUES': 'Add coded values',
    'DEL_CODEDVALUES': 'Delete coded values',
    'UPD_CODEDVALUEORDER': 'Update coded value order',
    'UPD_LEAFS': 'Update documents',
    'UPD_LEAFORDER': 'Update document order',
    'UPD_KEYORDER': 'Update key order',
    'UPD_NAMELABELWHERECLAUSE': 'Update variable name, label, where clause',
    'UPD_LOADACTUALDATA': 'Load data',
    'DEL_RESULTDISPLAY': 'Delete result display',
    'ADD_RESULTDISPLAY': 'Add result display',
    'ADD_RESULTDISPLAYS': 'Add result displays',
    'UPD_RESULTDISPLAY': 'Update result display',
    'UPD_RESULTDISPLAYORDER': 'Update result display order',
    'DEL_ANALYSISRESULT': 'Delete analysis result',
    'ADD_ANALYSISRESULT': 'Add analysis result',
    'ADD_ANALYSISRESULTS': 'Add analysis results',
    'UPD_ANALYSISRESULT': 'Update analysis result',
    'UPD_ANALYSISRESULTORDER': 'Update analysis result order',
    'ADD_REVIEWCOMMENT': 'Add comment',
    'DEL_REVIEWCOMMENT': 'Delete comment',
    'UPD_REVIEWCOMMENT': 'Update comment',
    'ADD_REPLYCOMMENT': 'Add reply',
    'UPD_RESOLVECOMMENT': 'Toggle comment resolution',
    'ADD_IMPORTMETADATA': 'Import Metadata',
    'DUMMY_ACTION': 'Technical action',
    'DEL_DUPLICATECOMMENTS': 'Remove duplicate comments',
    'DEL_DUPLICATEMETHODS': 'Remove duplicate methods',
    'DEFINE_UPD': 'Update Define-XML properties',
};
