/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018 <PERSON>losov                                                *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

// Import language configurations
import en from './en.js';
import zhCN from './zh-CN.js';

// Language configuration
export const languages = {
    en: {
        name: 'English',
        nativeName: 'English',
        code: 'en',
        translations: en
    },
    'zh-CN': {
        name: 'Simplified Chinese',
        nativeName: '简体中文',
        code: 'zh-CN',
        translations: zhCN
    }
};

// Get available language codes
export const getAvailableLanguages = () => {
    return Object.keys(languages);
};

// Get language info by code
export const getLanguageInfo = (code) => {
    return languages[code] || null;
};

// Get all translations for a specific language
export const getTranslations = (languageCode) => {
    const language = languages[languageCode];
    return language ? language.translations : null;
};

// Check if a language is supported
export const isLanguageSupported = (languageCode) => {
    return languageCode in languages;
};

// Get default language
export const getDefaultLanguage = () => {
    return 'en';
};

// Export all translations (for backward compatibility)
export const translations = Object.keys(languages).reduce((acc, code) => {
    acc[code] = languages[code].translations;
    return acc;
}, {});

export default {
    languages,
    getAvailableLanguages,
    getLanguageInfo,
    getTranslations,
    isLanguageSupported,
    getDefaultLanguage,
    translations
};
