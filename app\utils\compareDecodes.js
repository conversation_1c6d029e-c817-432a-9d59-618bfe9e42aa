/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018, 2019 <PERSON>                                          *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHA<PERSON><PERSON>ILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

function compareDecodes (decodes1, decodes2) {
    if (decodes1.length !== decodes2.length) {
        return false;
    } else {
        return !decodes1.some((decode1, index) => {
            let decode2 = decodes2[index];
            return (Object.keys(decode1).some(prop => (decode1[prop] !== decode2[prop])));
        });
    }
}

export default compareDecodes;
