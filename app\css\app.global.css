/*
 * @NOTE: Prepend a `~` to css file paths that are in your node_modules
 *        See https://github.com/webpack-contrib/sass-loader#imports
 */
@import 'prism.css';
@import '~bootstrap/dist/css/bootstrap.css';
@import '~react-bootstrap-table/dist/react-bootstrap-table-all.min.css';

@import '~typeface-roboto/index.css';
@import '~typeface-roboto-mono/index.css';

.specialHighlight {
    color: #AAAAAA !important;
}

.vlmRow:nth-of-type(odd){
    background-color: #E3F2FD !important;
}

.vlmRow:nth-of-type(odd):hover {
    background-color: #BBDEFB !important;
}

.vlmRow:nth-of-type(even){
    background-color: #E1F5FE !important;
}

.vlmRow:nth-of-type(even):hover {
    background-color: #B3E5FC !important;
}

.react-bs-container-body {
  width: 100%;
  overflow: visible;
}

@media print {
    .doNotPrint{
        display: none !important;
    }
}

div.htmlContent p {
    margin-bottom: 0;
}

div.htmlContent {
    white-space: normal;
}
