/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018, 2019 <PERSON>                                          *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

import React from 'react';
import { TableHeaderColumn } from 'react-bootstrap-table';

// Transform columns object to Bootstrap-react-table column headers;
function renderColumns (columns) {
    let result = [];
    Object.keys(columns).forEach(id => {
        let colProps = { dataField: id };
        let text = null;
        Object.keys(columns[id]).forEach((key) => {
            if (key !== 'text') {
                colProps[key] = columns[id][key];
            } else {
                text = columns[id].text;
            }
        });
        result.push(<TableHeaderColumn key={text} {...colProps}>{text}</TableHeaderColumn>);
    });
    return result;
}

export default renderColumns;
