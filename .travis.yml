sudo: true

language: node_js

node_js:
  - node
  - 12

cache:
  yarn: true
  directories:
    - node_modules
    - app/node_modules

addons:
  apt:
    sources:
      - ubuntu-toolchain-r-test
    packages:
      - g++-4.8
      - icnsutils
      - graphicsmagick
      - xz-utils
      - xorriso

install:
  - export CXX="g++-4.8"
  - yarn
  - cd app && yarn && cd ..
  - "/sbin/start-stop-daemon --start --quiet --pidfile /tmp/custom_xvfb_99.pid --make-pidfile --background --exec /usr/bin/Xvfb -- :99 -ac -screen 0 1280x1024x16"

before_script:
  - export DISPLAY=:99.0
  - sh -e /etc/init.d/xvfb start &
  - sleep 3

script:
  - node --version
  - yarn package-all
