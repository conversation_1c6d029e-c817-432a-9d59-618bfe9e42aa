/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018 <PERSON>ov                                                *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

// English translations
export default {
    // Main Menu
    'Studies': 'Studies',
    'Editor': 'Editor',
    'Controlled Terminology': 'Controlled Terminology',
    'CDISC Library': 'CDISC Library',
    'Settings': 'Settings',
    'About': 'About',
    'Keyboard Shortcuts': 'Keyboard Shortcuts',
    'Update': 'Update',
    'Find in Page': 'Find in Page',
    'Search Studies': 'Search Studies',
    'History': 'History',
    'Save': 'Save',
    'Save As': 'Save As',
    'Print': 'Print',
    'Comments/Methods': 'Comments/Methods',
    'Tools': 'Tools',
    'Review Mode': 'Review Mode',
    'Quit': 'Quit',

    // Settings
    'General Settings': 'General Settings',
    'System': 'System',
    'User Name': 'User Name',
    'Controlled Terminology Folder': 'Controlled Terminology Folder',
    'Language': 'Language',
    'English': 'English',
    'Simplified Chinese': 'Simplified Chinese',

    // Common
    'Search': 'Search',
    'Cancel': 'Cancel',
    'OK': 'OK',
    'Yes': 'Yes',
    'No': 'No',
    'Close': 'Close',
    'Open': 'Open',
    'Delete': 'Delete',
    'Edit': 'Edit',
    'Add': 'Add',
    'Remove': 'Remove',
    'Copy': 'Copy',
    'Paste': 'Paste',
    'Cut': 'Cut',
    'Undo': 'Undo',
    'Redo': 'Redo',
    'Confirm Close': 'Confirm Close',
    'You have not saved changes to a comment. By continuing all of the unsaved changes will be lost.': 'You have not saved changes to a comment. By continuing all of the unsaved changes will be lost.',
    'Continue': 'Continue',
    'Reply': 'Reply',
    'Hide': 'Hide',
    'Show': 'Show',
    'replies': 'replies',
    'Unresolve': 'Unresolve',
    'Resolve': 'Resolve',

    // File Menu
    'File': 'File',
    'Save As Define-XML 2.0': 'Save As Define-XML 2.0',
    'View': 'View',
    'Help': 'Help',

    // Editor
    'No Define-XML documents are selected for editing. Select a Define-XML document to edit on the': 'No Define-XML documents are selected for editing. Select a Define-XML document to edit on the',
    'page.': 'page.',
    'Loading Define-XML.': 'Loading Define-XML.',
    'Taking too long? Use Ctrl+M to open the menu.': 'Taking too long? Use Ctrl+M to open the menu.',

    // Editor Tabs
    'Standards': 'Standards',
    'Datasets': 'Datasets',
    'Variables': 'Variables',
    'Codelists': 'Codelists',
    'Coded Values': 'Coded Values',
    'Documents': 'Documents',
    'Result Displays': 'Result Displays',
    'Analysis Results': 'Analysis Results',
    'Review Comments': 'Review Comments',

    // Table Headers and Labels
    'Name': 'Name',
    'Label': 'Label',
    'Type': 'Type',
    'Length': 'Length',
    'Order': 'Order',
    'Description': 'Description',
    'Comment': 'Comment',
    'Method': 'Method',
    'Value': 'Value',
    'Code': 'Code',
    'Decode': 'Decode',
    'Status': 'Status',
    'Version': 'Version',
    'Date': 'Date',
    'Author': 'Author',
    'Title': 'Title',
    'Location': 'Location',
    'Format': 'Format',
    'Class': 'Class',
    'Structure': 'Structure',
    'Purpose': 'Purpose',
    'Keys': 'Keys',
    'Mandatory': 'Mandatory',
    'Repeating': 'Repeating',
    'Reference': 'Reference',
    'Standard': 'Standard',
    'Domain': 'Domain',
    'Variable': 'Variable',
    'Dataset': 'Dataset',
    'Codelist': 'Codelist',

    // Buttons and Actions
    'Update': 'Update',
    'Columns': 'Columns',
    'Import Metadata': 'Import Metadata',
    'Add Variable': 'Add Variable',
    'Add Dataset': 'Add Dataset',
    'New Variable': 'New Variable',
    'New Dataset': 'New Dataset',
    'This Define': 'This Define',
    'Another Define': 'Another Define',
    'Options': 'Options',
    'Global Variables & Study OID': 'Global Variables & Study OID',
    'Study OID': 'Study OID',
    'Study Name': 'Study Name',
    'Protocol Name': 'Protocol Name',
    'Analysis Variables': 'Analysis Variables',
    'Add Reference Dataset': 'Add Reference Dataset',
    'Remove Variable': 'Remove Variable',

    // Search and Filter
    'Ctrl+F': 'Ctrl+F',
    'Expand VLM': 'Expand VLM',
    'Collapse VLM': 'Collapse VLM',
    'Filter': 'Filter',

    // Standards Tab
    'Global Variables & Study OID': 'Global Variables & Study OID',
    'Study Description': 'Study Description',
    'MetaDataVersion': 'MetaDataVersion',
    'Standard': 'Standard',
    'Controlled Terminology': 'Controlled Terminology',
    'ODM Attributes': 'ODM Attributes',
    'Other Attributes': 'Other Attributes',
    'Remove Standard': 'Remove Standard',
    'Analysis Results Metadata': 'Analysis Results Metadata',
    'File OID': 'File OID',
    'As Of Date Time': 'As Of Date Time',
    'Originator': 'Originator',
    'Stylesheet Location': 'Stylesheet Location',
    'Path to File': 'Path to File',
    'Last Modified': 'Last Modified',
    'ODM Attributes & Stylesheet location': 'ODM Attributes & Stylesheet location',
    'Sponsor Name': 'Sponsor Name',
    'Database Query Datetime': 'Database Query Datetime',
    'Visual Define-XML Editor Attributes': 'Visual Define-XML Editor Attributes',
    'Define-XML Name': 'Define-XML Name',
    'Define-XML Location': 'Define-XML Location',
    'Model': 'Model',
    'Version': 'Version',
    '# Codelists': '# Codelists',
    'Remove Controlled Terminology': 'Remove Controlled Terminology',
    'Add Controlled Terminology': 'Add Controlled Terminology',

    // XML Generation Comments
    'ItemGroup Definitions': 'ItemGroup Definitions',
    'ItemDef Definitions': 'ItemDef Definitions',
    'Codelist Definitions': 'Codelist Definitions',
    'Method Definitions': 'Method Definitions',
    'Comment Definitions': 'Comment Definitions',
    'Leaf Definitions': 'Leaf Definitions',
    'Analysis Result Display Definitions': 'Analysis Result Display Definitions',

    // Standard Constants - Code List Types
    'Enumeration': 'Enumeration',
    'Decoded': 'Decoded',
    'External': 'External',

    // Standard Constants - Origin Types
    'Derived': 'Derived',
    'Assigned': 'Assigned',
    'Predecessor': 'Predecessor',
    'CRF': 'CRF',
    'Protocol': 'Protocol',
    'eDT': 'eDT',
    'COLLECTED': 'COLLECTED',
    'DERIVED': 'DERIVED',
    'OTHER': 'OTHER',
    'NOT AVAILABLE': 'NOT AVAILABLE',

    // Standard Constants - Document Types
    'Annotated CRF': 'Annotated CRF',
    'Supplemental Document': 'Supplemental Document',
    'Other': 'Other',

    // Standard Constants - Class Types
    'SUBJECT LEVEL ANALYSIS DATASET': 'SUBJECT LEVEL ANALYSIS DATASET',
    'OCCURRENCE DATA STRUCTURE': 'OCCURRENCE DATA STRUCTURE',
    'BASIC DATA STRUCTURE': 'BASIC DATA STRUCTURE',
    'INTEGRATED SUBJECT LEVEL': 'INTEGRATED SUBJECT LEVEL',
    'INTEGRATED OCCURRENCE DATA STRUCTURE': 'INTEGRATED OCCURRENCE DATA STRUCTURE',
    'INTEGRATED BASIC DATA STRUCTURE': 'INTEGRATED BASIC DATA STRUCTURE',
    'ADAM OTHER': 'ADAM OTHER',
    'TRIAL DESIGN': 'TRIAL DESIGN',
    'SPECIAL PURPOSE': 'SPECIAL PURPOSE',
    'INTERVENTIONS': 'INTERVENTIONS',
    'EVENTS': 'EVENTS',
    'FINDINGS': 'FINDINGS',
    'FINDINGS ABOUT': 'FINDINGS ABOUT',
    'RELATIONSHIP': 'RELATIONSHIP',
    'STUDY REFERENCE': 'STUDY REFERENCE',

    // Standard Constants - Variable Roles
    'Identifier': 'Identifier',
    'Topic': 'Topic',
    'Timing': 'Timing',
    'Grouping Qualifier': 'Grouping Qualifier',
    'Result Qualifier': 'Result Qualifier',
    'Synonym Qualifier': 'Synonym Qualifier',
    'Record Qualifier': 'Record Qualifier',
    'Variable Qualifier': 'Variable Qualifier',
    'Rule': 'Rule',

    // Standard Constants - ARM Analysis
    'Data Driven': 'Data Driven',
    'Requested by Regulatory Agency': 'Requested by Regulatory Agency',
    'Specified in Protocol': 'Specified in Protocol',
    'Specified in SAP': 'Specified in SAP',
    'Exploratory Outcome Measure': 'Exploratory Outcome Measure',
    'Primary Outcome Measure': 'Primary Outcome Measure',
    'Secondary Outcome Measure': 'Secondary Outcome Measure',
};
