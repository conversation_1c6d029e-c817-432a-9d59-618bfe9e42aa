/***********************************************************************************
* This file is part of Visual Define-XML Editor. A program which allows to review  *
* and edit XML files created using the CDISC Define-XML standard.                  *
* Copyright (C) 2018 Dmitry Kolosov                                                *
*                                                                                  *
* Visual Define-XML Editor is free software: you can redistribute it and/or modify *
* it under the terms of version 3 of the GNU Affero General Public License         *
*                                                                                  *
* Visual Define-XML Editor is distributed in the hope that it will be useful,      *
* but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY   *
* or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License   *
* version 3 (http://www.gnu.org/licenses/agpl-3.0.txt) for more details.           *
***********************************************************************************/

import columns from 'constants/columns';
import { t, tWithLang } from '../utils/i18n.js';

const dataTypes = [
    'text',
    'integer',
    'float',
    'date',
    'datetime',
    'time',
    'partialDate',
    'partialTime',
    'partialDatetime',
    'incompleteDatetime',
    'durationDatetime',
];

const getCodeListTypes = () => [
    { 'enumerated': t('Enumeration') },
    { 'decoded': t('Decoded') },
    { 'external': t('External') },
];

const codeListTypes = getCodeListTypes();

const comparators = ['EQ', 'NE', 'LT', 'LE', 'GT', 'GE', 'IN', 'NOTIN'];

const standardNames = {
    '2.0.0': [
        'SDTM-IG',
        'SDTM-IG-MD',
        'SDTM-IG-AP',
        'SDTM-IG-PGx',
        'SEND-IG',
        'SEND-IG-DART',
        'ADaM-IG',
    ],
    '2.1.0': [
        'SDTMIG',
        'SDTMIG-MD',
        'SDTMIG-AP',
        'SDTMIG-PGx',
        'SENDIG',
        'SENDIG-DART',
        'ADaMIG',
    ],
};

const getOriginTypes = () => ({
    'ADaM': [
        t('Derived'),
        t('Assigned'),
        t('Predecessor')
    ],
    'SDTM': [
        t('CRF'),
        t('Derived'),
        t('Assigned'),
        t('Protocol'),
        t('eDT'),
        t('Predecessor')
    ],
    'SEND': [
        t('COLLECTED'),
        t('DERIVED'),
        t('OTHER'),
        t('NOT AVAILABLE'),
    ],
});

// Function to get origin types with specific language
const getOriginTypesWithLang = (language) => ({
    'ADaM': [
        tWithLang('Derived', language),
        tWithLang('Assigned', language),
        tWithLang('Predecessor', language)
    ],
    'SDTM': [
        tWithLang('CRF', language),
        tWithLang('Derived', language),
        tWithLang('Assigned', language),
        tWithLang('Protocol', language),
        tWithLang('eDT', language),
        tWithLang('Predecessor', language)
    ],
    'SEND': [
        tWithLang('COLLECTED', language),
        tWithLang('DERIVED', language),
        tWithLang('OTHER', language),
        tWithLang('NOT AVAILABLE', language),
    ],
});

const originTypes = getOriginTypes();

const getTypeLabel = () => ({
    annotatedCrf: t('Annotated CRF'),
    supplementalDoc: t('Supplemental Document'),
    other: t('Other'),
});

const typeLabel = getTypeLabel();

const typeOrder = {
    annotatedCrf: 1,
    supplementalDoc: 2,
    other: 3,
};

const documentTypes = {
    typeOrder,
    typeLabel,
};

const getClassTypes = () => ({
    'ADaM': {
        [t('SUBJECT LEVEL ANALYSIS DATASET')]: 'ADSL',
        [t('OCCURRENCE DATA STRUCTURE')]: 'OCCDS',
        [t('BASIC DATA STRUCTURE')]: 'BDS',
        [t('INTEGRATED SUBJECT LEVEL')]: 'IADSL',
        [t('INTEGRATED OCCURRENCE DATA STRUCTURE')]: 'IOCCDS',
        [t('INTEGRATED BASIC DATA STRUCTURE')]: 'IBDS',
        [t('ADAM OTHER')]: 'Other',
    },
    'SDTM': {
        [t('TRIAL DESIGN')]: 'TD',
        [t('SPECIAL PURPOSE')]: 'SP',
        [t('INTERVENTIONS')]: 'INTERV',
        [t('EVENTS')]: 'EVENTS',
        [t('FINDINGS')]: 'FIND',
        [t('FINDINGS ABOUT')]: 'FA',
        [t('RELATIONSHIP')]: 'REL',
        [t('STUDY REFERENCE')]: 'STREF',
    },
    'SEND': {
        [t('TRIAL DESIGN')]: 'TD',
        [t('SPECIAL PURPOSE')]: 'SP',
        [t('INTERVENTIONS')]: 'INTERV',
        [t('EVENTS')]: 'EVENTS',
        [t('FINDINGS')]: 'FIND',
        [t('FINDINGS ABOUT')]: 'FA',
        [t('RELATIONSHIP')]: 'REL',
        [t('STUDY REFERENCE')]: 'STREF',
    }
});

const classTypes = getClassTypes();

const getVariableRoles = () => [
    t('Identifier'),
    t('Topic'),
    t('Timing'),
    t('Grouping Qualifier'),
    t('Result Qualifier'),
    t('Synonym Qualifier'),
    t('Record Qualifier'),
    t('Variable Qualifier'),
    t('Rule'),
];

const variableRoles = getVariableRoles();

const getArmAnalysisReason = () => ({
    'DATA DRIVEN': t('Data Driven'),
    'REQUESTED BY REGULATORY AGENCY': t('Requested by Regulatory Agency'),
    'SPECIFIED IN PROTOCOL': t('Specified in Protocol'),
    'SPECIFIED IN SAP': t('Specified in SAP'),
});

const armAnalysisReason = getArmAnalysisReason();

const getArmAnalysisPurpose = () => ({
    'EXPLORATORY OUTCOME MEASURE': t('Exploratory Outcome Measure'),
    'PRIMARY OUTCOME MEASURE': t('Primary Outcome Measure'),
    'SECONDARY OUTCOME MEASURE': t('Secondary Outcome Measure'),
});

const armAnalysisPurpose = getArmAnalysisPurpose();

// Function to refresh localized constants when language changes
const refreshLocalizedConstants = () => {
    return {
        dataTypes,
        codeListTypes: getCodeListTypes(),
        standardNames,
        documentTypes: {
            typeOrder,
            typeLabel: getTypeLabel(),
        },
        columns,
        originTypes: getOriginTypes(),
        comparators,
        classTypes: getClassTypes(),
        variableRoles: getVariableRoles(),
        armAnalysisReason: getArmAnalysisReason(),
        armAnalysisPurpose: getArmAnalysisPurpose(),
    };
};

const stdConstants = {
    dataTypes,
    codeListTypes,
    standardNames,
    documentTypes,
    columns,
    originTypes,
    comparators,
    classTypes,
    variableRoles,
    armAnalysisReason,
    armAnalysisPurpose,
    // Getter functions for dynamic localization
    getCodeListTypes,
    getOriginTypes,
    getOriginTypesWithLang,
    getTypeLabel,
    getClassTypes,
    getVariableRoles,
    getArmAnalysisReason,
    getArmAnalysisPurpose,
    refreshLocalizedConstants,
};

export default stdConstants;
